import 'package:app/i18n/i18n.dart';
import 'package:app/receivings_module/models/filter/filter_value.dart';
import 'package:app/receivings_module/widgets/filter/multi_select.dart';
import 'package:app/shared/cubits/filter/cubit.dart';
import 'package:app/shared/cubits/filter/state.dart';
import 'package:app/shared/widgets/filters/date_range.dart';
import 'package:app/shared/widgets/filters/list.dart';
import 'package:app/shared/widgets/filters/numeric.dart';
import 'package:app/shared/widgets/filters/text.dart';
import 'package:fl_ui/fl_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FilteringFilterButton<T extends FilterCubit> extends StatelessWidget {
  const FilteringFilterButton({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<T, FilterState>(
      builder: (context, state) {
        int selectedCount = state.values.entries
            .where((e) => e.value.value != null && e.value.value != '')
            .fold<int>(0, (count, f) {
          if (state.config.advancedFilterConfig
              .map((f) => f.id)
              .contains(f.key)) {
            if (f.value.value is Map) {
              return count + (f.value.value as Map).length;
            } else {
              return count + 1;
            }
          }

          return count;
        });

        final tr = context.translator;

        return FLFilterButton.filter(
          text: tr(Labels.filter.filtering) + ' ($selectedCount)',
          onPressed: state.isUpdating
              ? null
              : () {
                  final cubit = context.read<T>();
                  final multiSelectFilter = state.config.advancedFilterConfig.firstWhere(
                    (f) => f.id == 'multiSelect',
                  );

                  FLModalBottomSheet.showMultiPage(
                    context: context,
                    builder: (context, sheet) {
                      return BlocProvider.value(
                        value: cubit,
                        child: sheet,
                      );
                    },
                    pagesBuilder: (context, controller) {
                      return [
                        FLSliverModalBottomSheetPage(
                          title: FLModalBottomSheetTitle.regular(
                            title: tr(Labels.filter.label),
                            showDivider: true,
                          ),
                          mainContentSlivers: [
                            SliverList(
                              delegate: SliverChildBuilderDelegate(
                                (context, index) {
                                  final f =
                                      state.config.advancedFilterConfig[index];

                                  return f.map<Widget>(
                                    query: (_) => const SizedBox.shrink(),
                                    list: (f) => ListFilter<T>(filter: f),
                                    dateRange: (f) =>
                                        DateRangeFilter<T>(filter: f),
                                    text: (f) => TextFilter<T>(filter: f),
                                    numeric: (f) => NumericFilter<T>(filter: f),
                                    multiSelect: (f) => MultiSelectFilter<T>(
                                      filter: f,
                                      onTapCallbackNavigate: () {
                                        controller.switchTo(1);
                                      },
                                    ),
                                  );
                                },
                                childCount:
                                    state.config.advancedFilterConfig.length,
                              ),
                            ),
                          ],
                        ),
                        FLSliverModalBottomSheetPage(
                          title: FLModalBottomSheetTitle.regular(
                            title: tr(Labels.filter.label),
                            showDivider: true,
                          ),
                          mainContentSlivers: [
                            SliverList(
                              delegate: SliverChildBuilderDelegate(
                                (context, index) {
                                  final f =
                                      state.config.advancedFilterConfig[index];

                                  return f.map<Widget>(
                                    query: (_) => const SizedBox.shrink(),
                                    list: (_) => const SizedBox.shrink(),
                                    dateRange: (_) => const SizedBox.shrink(),
                                    text: (_) => const SizedBox.shrink(),
                                    numeric: (_) => const SizedBox.shrink(),
                                    multiSelect: (f) =>
                                        MultiSelectFilterPage<T>(
                                      filter: f,
                                      state: state,
                                      controller: controller,
                                    ),
                                  );
                                },
                                childCount:
                                    state.config.advancedFilterConfig.length,
                              ),
                            ),
                          ],
                          actionBar: FLModalBottomSheetActionBar.custom(
                            child: _ActionButtons(
                              onApply: () => controller.switchTo(0),
                              onClear: () => cubit.add(
                                id: multiSelectFilter.id,
                                value: const FilterValueModel<String>(value: ''),
                              ),
                            ),
                          ),
                        ),
                      ];
                    },
                  );
                },
        );
      },
    );
  }
}

class _ActionButtons<T extends FilterCubit> extends StatelessWidget
    implements PreferredSizeWidget {
  final VoidCallback onApply;
  final VoidCallback onClear;

  const _ActionButtons({
    Key? key,
    required this.onClear,
    required this.onApply,
  }) : super(key: key);

  @override
  Size get preferredSize => const Size.fromHeight(52);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: FLSpacings.md,
        vertical: FLSpacings.sm,
      ),
      child: Row(
        children: [
          Expanded(
            child: FLTextButton(
              text: 'Clear All',
              onPressed: onClear,
            ),
          ),
          FLGaps.xmd,
          Expanded(
            child: FLFilledButton.text(
              text: 'Apply',
              onPressed: onApply,
            ),
          ),
        ],
      ),
    );
  }
}
